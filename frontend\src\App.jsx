import { BrowserRouter as Router, Routes, Route, Link } from 'react-router-dom'
import './App.css'
import Home from './components/Home'
import Vouchers from './components/Vouchers'
import LedgerStatement from './components/LedgerStatement'
import CreateLedger from './components/CreateLedger'
import StockItem from './components/StockItem'
import Daybook from './components/Daybook'
import VoucherCreate from './components/VoucherCreate'

function App() {
  return (
    <Router>
      <div className="app">
        <nav className="navbar">
          <div className="nav-brand">
            <h2>Tally Management System</h2>
          </div>
          <div className="nav-links">
            <Link to="/" className="nav-link">Home</Link>
            <Link to="/vouchers" className="nav-link">Vouchers</Link>
            <Link to="/ledger-statement" className="nav-link">Ledger Statement</Link>
            <Link to="/create-ledger" className="nav-link">Create Ledger</Link>
            <Link to="/stock-item" className="nav-link">Stock Item</Link>
            <Link to="/daybook" className="nav-link">Daybook</Link>
            <Link to="/create-voucher" className="nav-link">Create Voucher</Link>
          </div>
        </nav>

        <main className="main-content">
          <Routes>
            <Route path="/" element={<Home />} />
            <Route path="/vouchers" element={<Vouchers />} />
            <Route path="/ledger-statement" element={<LedgerStatement />} />
            <Route path="/create-ledger" element={<CreateLedger />} />
            <Route path="/stock-item" element={<StockItem />} />
            <Route path="/daybook" element={<Daybook />} />
            <Route path="/create-voucher" element={<VoucherCreate />} />
          </Routes>
        </main>
      </div>
    </Router>
  )
}

export default App
