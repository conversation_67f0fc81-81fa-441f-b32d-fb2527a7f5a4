import requests
from xml.etree import ElementTree as Et
from tkinter import Tk, Entry, Button, font


class MainWindow:

    def __init__(self, main):
        self.font_14 = font.Font(family='Roboto', size=14)
        # button
        get_button = Button(main, text="Get Companies", bd=2, font=self.font_14, bg='blue', fg='white',
                            command=self.get_cmp)
        get_button.grid(row=1, column=1, padx=15, pady=15, ipadx=5, ipady=5)

        # header
        cmp_name = Entry(main, font=self.font_14, bg="green", fg="white")
        cmp_name.insert(0, "NAME")
        cmp_name.grid(row=3, column=2, ipadx=5, ipady=5)

        cmp_number = Entry(main, font=self.font_14, bg="green", fg="white")
        cmp_number.insert(0, "NUMBER")
        cmp_number.grid(row=3, column=3, ipadx=5, ipady=5)

    def get_cmp(self):
        xml_body = '<ENVELOPE><HEADER><VERSION>1</VERSION><TALLYREQUEST>EXPORT</TALLYREQUEST><TYPE>COLLECTION</TYPE>'
        xml_body += '<ID>ListOfCompanies</ID></HEADER><BODY><DESC><STATICVARIABLES><SVEXPORTFORMAT>$$SysName:XML</SVEXPORTFORMAT>'
        xml_body += '</STATICVARIABLES><TDL><TDLMESSAGE><COLLECTION Name="ListOfCompanies"><TYPE>Company</TYPE>'
        xml_body += '<FETCH>Name,CompanyNumber</FETCH></COLLECTION></TDLMESSAGE></TDL></DESC></BODY></ENVELOPE>'

        req = requests.post(url="http://localhost:9000", data=xml_body)
        res = Et.fromstring(req.text.strip())
        row_count = 4
        for cmp in res.findall('./BODY/DATA/COLLECTION/COMPANY'):
            cmp_name = Entry(font=self.font_14, fg='green')
            cmp_name.insert(0,cmp.find('NAME').text)
            cmp_name.grid(row=row_count, column=2, ipadx=5, ipady=5)

            cmp_number = Entry(font=self.font_14, fg='green')
            cmp_number.insert(0, cmp.find('COMPANYNUMBER').text)
            cmp_number.grid(row=row_count, column=3, ipadx=5, ipady=5)

            row_count += 1


if __name__ == "__main__":
    root = Tk()
    root.geometry("700x600")
    root.title("Getting Company")
    MainWindow(root)
    root.mainloop()