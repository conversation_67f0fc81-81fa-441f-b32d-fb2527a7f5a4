from tkinter import Tk, Label, Entry, font, Button, messagebox
from requests import request
from xml.etree import ElementTree as Et


class MainWindow:
    def __init__(self, parent):
        self.font_14 = font.Font(family='Roboto', size=14)
        from_dt = Label(parent, text="From date:", font=self.font_14, fg="green")
        from_dt.grid(row=1, column=1, padx=10, pady=10)
        to_dt = Label(parent, text="To date:", font=self.font_14, fg="green")
        to_dt.grid(row=1, column=3, padx=10, pady=10)
        self.from_dt_inp = Entry(parent, bd=2, font=self.font_14, width=15)
        self.from_dt_inp.grid(row=1, column=2, ipadx=5, ipady=5, padx=10, pady=10)
        self.to_dt_inp = Entry(parent, bd=2, font=self.font_14, width=15)
        self.to_dt_inp.grid(row=1, column=4, ipadx=5, ipady=5, padx=10, pady=10)
        submit_button = Button(parent, text="Submit", font=self.font_14, bg="green", fg="white", bd=2, width=10,
                               command=self.submit_click)
        submit_button.grid(row=2, column=4, ipadx=2, ipady=2, padx=10, pady=10)

        vch_dt = Entry(font=self.font_14, bd=2, width=10, fg="white", bg="green")
        vch_dt.insert(0, "Date")
        vch_dt.grid(row=3, column=1, ipadx=5, ipady=5, pady=10)

        vch_led = Entry(font=self.font_14, bd=2, width=20, fg="white", bg="green")
        vch_led.insert(0, "Particulars")
        vch_led.grid(row=3, column=2, ipadx=5, ipady=5, pady=10)

        vch_type = Entry(font=self.font_14, bd=2, width=20, fg="white", bg="green")
        vch_type.insert(0, "Vch Type")
        vch_type.grid(row=3, column=3, ipadx=5, ipady=5, pady=10)

        vch_no = Entry(font=self.font_14, bd=2, width=20, fg="white", bg="green")
        vch_no.insert(0, "Vch No")
        vch_no.grid(row=3, column=4, ipadx=5, ipady=5, pady=10)

        vch_amount = Entry(font=self.font_14, bd=2, width=20, fg="white", bg="green")
        vch_amount.insert(0, "Amount")
        vch_amount.grid(row=3, column=5, ipadx=5, ipady=5, pady=10)

    def submit_click(self):
        from_dt = self.from_dt_inp.get().strip()
        to_dt = self.to_dt_inp.get().strip()
        xml = "<ENVELOPE><HEADER><VERSION>1</VERSION><TALLYREQUEST>EXPORT</TALLYREQUEST><TYPE>DATA</TYPE><ID>DayBook</ID>"
        xml += "</HEADER><BODY><DESC><STATICVARIABLES><SVEXPORTFORMAT>$$SysName:XML</SVEXPORTFORMAT><SVFROMDATE TYPE='DATE'>"
        xml += from_dt + "</SVFROMDATE><SVTODATE TYPE='DATE'>" + to_dt + "</SVTODATE></STATICVARIABLES></DESC></BODY></ENVELOPE>"

        daybook_res = self.get_data(xml)
        amount = 0
        row_count = 4
        for vch in daybook_res.findall("./BODY/DATA/TALLYMESSAGE/VOUCHER"):

            if len(vch.findall("ALLLEDGERENTRIES.LIST")) == 0:
                amount = vch.findall("LEDGERENTRIES.LIST").__getitem__(0).find("AMOUNT").text
            else:
                amount = vch.findall("ALLLEDGERENTRIES.LIST").__getitem__(0).find("AMOUNT").text

            vch_dt = Entry(font=self.font_14, bd=2, width=10)
            vch_dt.insert(0, self.format_date(vch.find("DATE").text))
            vch_dt.grid(row=row_count, column=1, ipadx=5, ipady=5, pady=10)

            vch_led = Entry(font=self.font_14, bd=2, width=20)
            vch_led.insert(0, vch.find("VOUCHERTYPENAME").text)
            vch_led.grid(row=row_count, column=2, ipadx=5, ipady=5, pady=10)

            vch_type = Entry(font=self.font_14, bd=2, width=20)
            vch_type.insert(0, vch.find("VOUCHERNUMBER").text)
            vch_type.grid(row=row_count, column=3, ipadx=5, ipady=5, pady=10)

            vch_no = Entry(font=self.font_14, bd=2, width=20)
            vch_no.insert(0, vch.find("PARTYLEDGERNAME").text)
            vch_no.grid(row=row_count, column=4, ipadx=5, ipady=5, pady=10)

            vch_amount = Entry(font=self.font_14, bd=2, width=20)
            vch_amount.insert(0, self.format_amount(float(amount)))
            vch_amount.grid(row=row_count, column=5, ipadx=5, ipady=5, pady=10)

            row_count += 1

    @staticmethod
    def format_amount(amt):
        if amt < 0:
            return str(amt * (-1)) + " Cr "

        return str(amt) + " Dr "

    @staticmethod
    def format_date(date):
        return date[6:8] + "-" + date[4:6] + "-" + date[0:4]

    @staticmethod
    def get_data(payload):
        req = request("GET", url="http://localhost:9000", data=payload)
        res = req.text.encode("UTF-8")
        return Et.fromstring(res)


if __name__ == "__main__":
    app = Tk()
    app.geometry("1080x600")
    app.title("DayBook")
    MainWindow(app)
    app.mainloop()