import requests
from xml.etree import ElementTree as Et
from datetime import datetime
import json

class TallyVoucherManager:
    """
    A comprehensive class for creating and managing vouchers in Tally
    Supports Sales, Purchase, Payment, and Receipt vouchers with multiple items
    Uses the correct Tally XML format for voucher import
    """

    def __init__(self, tally_url="http://localhost:9000"):
        self.tally_url = tally_url

    def send_to_tally(self, xml_data):
        """Send XML data to Tally and return response"""
        try:
            print(f"Sending XML to Tally:\n{xml_data}")
            response = requests.post(self.tally_url, data=xml_data, timeout=30)
            print(f"Tally Response:\n{response.text}")
            return response.text
        except requests.exceptions.RequestException as e:
            raise Exception(f"Failed to connect to Tally: {str(e)}")

    def format_date_for_tally(self, date_str):
        """Convert date from YYYY-MM-DD to YYYYMMDD format for Tally"""
        if isinstance(date_str, str):
            return date_str.replace('-', '')
        return date_str

    def parse_tally_response(self, response_xml):
        """Parse Tally response to check if voucher was created successfully"""
        try:
            root = Et.fromstring(response_xml)
            import_result = root.find('.//IMPORTRESULT')
            if import_result is not None:
                created = import_result.find('CREATED')
                errors = import_result.find('ERRORS')
                return {
                    'success': True,
                    'created': int(created.text) if created is not None else 0,
                    'errors': int(errors.text) if errors is not None else 0,
                    'message': f"Successfully created {created.text if created is not None else 0} voucher(s)"
                }
            else:
                return {
                    'success': False,
                    'message': 'Invalid response from Tally',
                    'response': response_xml
                }
        except Exception as e:
            return {
                'success': False,
                'message': f'Error parsing Tally response: {str(e)}',
                'response': response_xml
            }
    
    def create_sales_voucher(self, voucher_data):
        """
        Create a sales voucher in Tally

        voucher_data should contain:
        - date: Date in YYYY-MM-DD format
        - voucher_number: Voucher number
        - party_ledger: Customer ledger name
        - items: List of items with name, quantity, rate, amount (optional for simple vouchers)
        - narration: Optional narration
        """
        try:
            date_formatted = self.format_date_for_tally(voucher_data['date'])

            xml = self._build_voucher_xml(
                voucher_type="Sales",
                date=date_formatted,
                voucher_number=voucher_data['voucher_number'],
                party_ledger=voucher_data['party_ledger'],
                items=voucher_data.get('items', []),
                narration=voucher_data.get('narration', ''),
                amount=voucher_data.get('totalAmount', 0)
            )

            response = self.send_to_tally(xml)
            result = self.parse_tally_response(response)
            return result

        except Exception as e:
            return {
                'success': False,
                'message': f'Error creating sales voucher: {str(e)}',
                'response': None
            }
    
    def create_purchase_voucher(self, voucher_data):
        """
        Create a purchase voucher in Tally

        voucher_data should contain:
        - date: Date in YYYY-MM-DD format
        - voucher_number: Voucher number
        - party_ledger: Supplier ledger name
        - items: List of items with name, quantity, rate, amount (optional for simple vouchers)
        - narration: Optional narration
        """
        try:
            date_formatted = self.format_date_for_tally(voucher_data['date'])

            xml = self._build_voucher_xml(
                voucher_type="Purchase",
                date=date_formatted,
                voucher_number=voucher_data['voucher_number'],
                party_ledger=voucher_data['party_ledger'],
                items=voucher_data.get('items', []),
                narration=voucher_data.get('narration', ''),
                amount=voucher_data.get('totalAmount', 0)
            )

            response = self.send_to_tally(xml)
            result = self.parse_tally_response(response)
            return result

        except Exception as e:
            return {
                'success': False,
                'message': f'Error creating purchase voucher: {str(e)}',
                'response': None
            }

    def create_payment_voucher(self, voucher_data):
        """
        Create a payment voucher in Tally

        voucher_data should contain:
        - date: Date in YYYY-MM-DD format
        - voucher_number: Voucher number
        - from_ledger: Bank/Cash ledger (credit side)
        - to_ledger: Expense/Party ledger (debit side)
        - amount: Payment amount
        - narration: Optional narration
        """
        try:
            date_formatted = self.format_date_for_tally(voucher_data['date'])

            xml = self._build_payment_receipt_voucher_xml(
                voucher_type="Payment",
                date=date_formatted,
                voucher_number=voucher_data['voucher_number'],
                from_ledger=voucher_data['from_ledger'],
                to_ledger=voucher_data['to_ledger'],
                amount=voucher_data['amount'],
                narration=voucher_data.get('narration', '')
            )

            response = self.send_to_tally(xml)
            result = self.parse_tally_response(response)
            return result

        except Exception as e:
            return {
                'success': False,
                'message': f'Error creating payment voucher: {str(e)}',
                'response': None
            }

    def create_receipt_voucher(self, voucher_data):
        """
        Create a receipt voucher in Tally

        voucher_data should contain:
        - date: Date in YYYY-MM-DD format
        - voucher_number: Voucher number
        - from_ledger: Party/Income ledger (credit side)
        - to_ledger: Bank/Cash ledger (debit side)
        - amount: Receipt amount
        - narration: Optional narration
        """
        try:
            date_formatted = self.format_date_for_tally(voucher_data['date'])

            xml = self._build_payment_receipt_voucher_xml(
                voucher_type="Receipt",
                date=date_formatted,
                voucher_number=voucher_data['voucher_number'],
                from_ledger=voucher_data['from_ledger'],
                to_ledger=voucher_data['to_ledger'],
                amount=voucher_data['amount'],
                narration=voucher_data.get('narration', '')
            )

            response = self.send_to_tally(xml)
            result = self.parse_tally_response(response)
            return result

        except Exception as e:
            return {
                'success': False,
                'message': f'Error creating receipt voucher: {str(e)}',
                'response': None
            }
    
    def _build_voucher_xml(self, voucher_type, date, voucher_number, party_ledger, items, narration, amount):
        """Build XML for Sales/Purchase vouchers using correct Tally format"""
        total_amount = float(amount) if amount else sum(float(item.get('amount', 0)) for item in items)

        # Start with the correct envelope structure
        xml = '<ENVELOPE>'
        xml += '<HEADER>'
        xml += '<VERSION>1</VERSION>'
        xml += '<TALLYREQUEST>Import</TALLYREQUEST>'
        xml += '<TYPE>Data</TYPE>'
        xml += '<ID>Vouchers</ID>'
        xml += '</HEADER>'
        xml += '<BODY>'
        xml += '<DESC>'
        xml += '<STATICVARIABLES>'
        xml += '<IMPORTDUPS>@@DUPCOMBINE</IMPORTDUPS>'
        xml += '</STATICVARIABLES>'
        xml += '</DESC>'
        xml += '<DATA>'
        xml += '<TALLYMESSAGE>'
        xml += '<VOUCHER>'
        xml += f'<DATE>{date}</DATE>'
        xml += f'<NARRATION>{narration}</NARRATION>'
        xml += f'<VOUCHERTYPENAME>{voucher_type}</VOUCHERTYPENAME>'
        xml += f'<VOUCHERNUMBER>{voucher_number}</VOUCHERNUMBER>'

        if voucher_type == "Sales":
            # For sales: Customer is debited (positive), Sales account is credited (negative)
            xml += '<ALLLEDGERENTRIES.LIST>'
            xml += f'<LEDGERNAME>{party_ledger}</LEDGERNAME>'
            xml += '<ISDEEMEDPOSITIVE>Yes</ISDEEMEDPOSITIVE>'
            xml += f'<AMOUNT>{total_amount}</AMOUNT>'
            xml += '</ALLLEDGERENTRIES.LIST>'

            xml += '<ALLLEDGERENTRIES.LIST>'
            xml += '<LEDGERNAME>Sales</LEDGERNAME>'
            xml += '<ISDEEMEDPOSITIVE>No</ISDEEMEDPOSITIVE>'
            xml += f'<AMOUNT>-{total_amount}</AMOUNT>'
            xml += '</ALLLEDGERENTRIES.LIST>'

        elif voucher_type == "Purchase":
            # For purchase: Purchase account is debited (positive), Supplier is credited (negative)
            xml += '<ALLLEDGERENTRIES.LIST>'
            xml += '<LEDGERNAME>Purchase</LEDGERNAME>'
            xml += '<ISDEEMEDPOSITIVE>Yes</ISDEEMEDPOSITIVE>'
            xml += f'<AMOUNT>{total_amount}</AMOUNT>'
            xml += '</ALLLEDGERENTRIES.LIST>'

            xml += '<ALLLEDGERENTRIES.LIST>'
            xml += f'<LEDGERNAME>{party_ledger}</LEDGERNAME>'
            xml += '<ISDEEMEDPOSITIVE>No</ISDEEMEDPOSITIVE>'
            xml += f'<AMOUNT>-{total_amount}</AMOUNT>'
            xml += '</ALLLEDGERENTRIES.LIST>'

        # Add inventory entries if items are provided
        for item in items:
            xml += '<ALLINVENTORYENTRIES.LIST>'
            xml += f'<STOCKITEMNAME>{item["name"]}</STOCKITEMNAME>'
            if voucher_type == "Sales":
                xml += '<ISDEEMEDPOSITIVE>Yes</ISDEEMEDPOSITIVE>'
                xml += f'<AMOUNT>{item["amount"]}</AMOUNT>'
                xml += f'<ACTUALQTY>{item["quantity"]}</ACTUALQTY>'
                xml += f'<BILLEDQTY>{item["quantity"]}</BILLEDQTY>'
            else:  # Purchase
                xml += '<ISDEEMEDPOSITIVE>No</ISDEEMEDPOSITIVE>'
                xml += f'<AMOUNT>-{item["amount"]}</AMOUNT>'
                xml += f'<ACTUALQTY>-{item["quantity"]}</ACTUALQTY>'
                xml += f'<BILLEDQTY>-{item["quantity"]}</BILLEDQTY>'
            xml += f'<RATE>{item["rate"]}</RATE>'
            xml += '</ALLINVENTORYENTRIES.LIST>'

        xml += '</VOUCHER>'
        xml += '</TALLYMESSAGE>'
        xml += '</DATA>'
        xml += '</BODY>'
        xml += '</ENVELOPE>'
        return xml
    
    def _build_payment_receipt_voucher_xml(self, voucher_type, date, voucher_number, from_ledger, to_ledger, amount, narration):
        """Build XML for Payment/Receipt vouchers using correct Tally format"""

        # Start with the correct envelope structure
        xml = '<ENVELOPE>'
        xml += '<HEADER>'
        xml += '<VERSION>1</VERSION>'
        xml += '<TALLYREQUEST>Import</TALLYREQUEST>'
        xml += '<TYPE>Data</TYPE>'
        xml += '<ID>Vouchers</ID>'
        xml += '</HEADER>'
        xml += '<BODY>'
        xml += '<DESC>'
        xml += '<STATICVARIABLES>'
        xml += '<IMPORTDUPS>@@DUPCOMBINE</IMPORTDUPS>'
        xml += '</STATICVARIABLES>'
        xml += '</DESC>'
        xml += '<DATA>'
        xml += '<TALLYMESSAGE>'
        xml += '<VOUCHER>'
        xml += f'<DATE>{date}</DATE>'
        xml += f'<NARRATION>{narration}</NARRATION>'
        xml += f'<VOUCHERTYPENAME>{voucher_type}</VOUCHERTYPENAME>'
        xml += f'<VOUCHERNUMBER>{voucher_number}</VOUCHERNUMBER>'

        if voucher_type == "Payment":
            # For payment: Expense/Party is debited (positive), Bank/Cash is credited (negative)
            xml += '<ALLLEDGERENTRIES.LIST>'
            xml += f'<LEDGERNAME>{to_ledger}</LEDGERNAME>'
            xml += '<ISDEEMEDPOSITIVE>Yes</ISDEEMEDPOSITIVE>'
            xml += f'<AMOUNT>{amount}</AMOUNT>'
            xml += '</ALLLEDGERENTRIES.LIST>'

            xml += '<ALLLEDGERENTRIES.LIST>'
            xml += f'<LEDGERNAME>{from_ledger}</LEDGERNAME>'
            xml += '<ISDEEMEDPOSITIVE>No</ISDEEMEDPOSITIVE>'
            xml += f'<AMOUNT>-{amount}</AMOUNT>'
            xml += '</ALLLEDGERENTRIES.LIST>'

        elif voucher_type == "Receipt":
            # For receipt: Bank/Cash is debited (positive), Income/Party is credited (negative)
            xml += '<ALLLEDGERENTRIES.LIST>'
            xml += f'<LEDGERNAME>{to_ledger}</LEDGERNAME>'
            xml += '<ISDEEMEDPOSITIVE>Yes</ISDEEMEDPOSITIVE>'
            xml += f'<AMOUNT>{amount}</AMOUNT>'
            xml += '</ALLLEDGERENTRIES.LIST>'

            xml += '<ALLLEDGERENTRIES.LIST>'
            xml += f'<LEDGERNAME>{from_ledger}</LEDGERNAME>'
            xml += '<ISDEEMEDPOSITIVE>No</ISDEEMEDPOSITIVE>'
            xml += f'<AMOUNT>-{amount}</AMOUNT>'
            xml += '</ALLLEDGERENTRIES.LIST>'

        xml += '</VOUCHER>'
        xml += '</TALLYMESSAGE>'
        xml += '</DATA>'
        xml += '</BODY>'
        xml += '</ENVELOPE>'
        return xml
    
    def create_voucher(self, voucher_type, voucher_data):
        """
        Generic method to create voucher based on type

        Args:
            voucher_type: 'Sales', 'Purchase', 'Payment', or 'Receipt'
            voucher_data: Dictionary containing voucher details
        """
        voucher_type_lower = voucher_type.lower()

        if voucher_type_lower == 'sales':
            return self.create_sales_voucher(voucher_data)
        elif voucher_type_lower == 'purchase':
            return self.create_purchase_voucher(voucher_data)
        elif voucher_type_lower == 'payment':
            return self.create_payment_voucher(voucher_data)
        elif voucher_type_lower == 'receipt':
            return self.create_receipt_voucher(voucher_data)
        else:
            return {
                'success': False,
                'message': f'Unsupported voucher type: {voucher_type}. Supported types: Sales, Purchase, Payment, Receipt',
                'response': None
            }

# Example usage and testing functions
def test_sales_voucher():
    """Test function for creating a sales voucher"""
    voucher_manager = TallyVoucherManager()

    sample_sales_data = {
        'date': '2024-01-15',
        'voucher_number': 'TEST-S001',
        'party_ledger': 'Test Customer',
        'totalAmount': 350.00,
        'items': [
            {
                'name': 'Product A',
                'quantity': '2',
                'rate': '100.00',
                'amount': '200.00'
            },
            {
                'name': 'Product B',
                'quantity': '1',
                'rate': '150.00',
                'amount': '150.00'
            }
        ],
        'narration': 'Test Sales Voucher'
    }

    result = voucher_manager.create_sales_voucher(sample_sales_data)
    print("Sales Voucher Result:", json.dumps(result, indent=2))
    return result

def test_purchase_voucher():
    """Test function for creating a purchase voucher"""
    voucher_manager = TallyVoucherManager()

    sample_purchase_data = {
        'date': '2024-01-15',
        'voucher_number': 'TEST-P001',
        'party_ledger': 'Test Supplier',
        'totalAmount': 250.00,
        'items': [
            {
                'name': 'Raw Material A',
                'quantity': '5',
                'rate': '50.00',
                'amount': '250.00'
            }
        ],
        'narration': 'Test Purchase Voucher'
    }

    result = voucher_manager.create_purchase_voucher(sample_purchase_data)
    print("Purchase Voucher Result:", json.dumps(result, indent=2))
    return result

def test_payment_voucher():
    """Test function for creating a payment voucher"""
    voucher_manager = TallyVoucherManager()

    sample_payment_data = {
        'date': '2024-01-15',
        'voucher_number': 'TEST-PAY001',
        'from_ledger': 'Bank of India',
        'to_ledger': 'Conveyance',
        'amount': 12000.00,
        'narration': 'Test Payment Voucher'
    }

    result = voucher_manager.create_payment_voucher(sample_payment_data)
    print("Payment Voucher Result:", json.dumps(result, indent=2))
    return result

def test_receipt_voucher():
    """Test function for creating a receipt voucher"""
    voucher_manager = TallyVoucherManager()

    sample_receipt_data = {
        'date': '2024-01-15',
        'voucher_number': 'TEST-REC001',
        'from_ledger': 'Test Customer',
        'to_ledger': 'Bank of India',
        'amount': 5000.00,
        'narration': 'Test Receipt Voucher'
    }

    result = voucher_manager.create_receipt_voucher(sample_receipt_data)
    print("Receipt Voucher Result:", json.dumps(result, indent=2))
    return result

if __name__ == "__main__":
    print("Tally Voucher Manager - Comprehensive Testing")
    print("=" * 50)
    print("Note: Make sure Tally is running with Gateway enabled on port 9000")
    print("=" * 50)

    # Test all voucher types
    print("\n1. Testing Sales Voucher Creation:")
    test_sales_voucher()

    print("\n2. Testing Purchase Voucher Creation:")
    test_purchase_voucher()

    print("\n3. Testing Payment Voucher Creation:")
    test_payment_voucher()

    print("\n4. Testing Receipt Voucher Creation:")
    test_receipt_voucher()

    print("\n" + "=" * 50)
    print("Testing completed! Check the results above.")
    print("If you see 'success': true, the vouchers were created in Tally.")
    print("=" * 50)
