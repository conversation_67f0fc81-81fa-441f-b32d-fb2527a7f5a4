.container{
margin :auto;
width : 300px;
border-radius:10px;
padding:30px;
box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);

}
.title{
color:Orange;
font-size:22px;
font-weight:bold;
text-align:center;
}

.vchTitle{
float:left;
}

label{
font-size:16px;
color:green;
}
.period{
float:right;
}

h2{
text-align:center;
color:green;
}

p{
font-size:14px;
color:green;
}

select{
font-size:18px;
width:200px;
height:30px;
padding:5px;
}
body{
font-family:Sans-Serif;
}
input[type=text]{
border:none;
outline:none;
border-bottom:1px solid black;
width:200px;
height:10px;
padding:5px;
font-size:18px;
}

input[type=text]:focus{
border-bottom:1px solid orange;
}

input[type=submit]{
margin-left:75px;
margin-right:75px;
font-size:18px;
height:40px;
width:150px;
padding:5px;
margin-top:25px;
background-color:orange;
color:white;
border:none;
outline:none;
border-radius:5px;
}

input[type=submit]:hover{
background-color:green;
}

table,th,td{
border-collapse:collapse;
border:1px solid black;
}

th,td{
padding:10px;
text-align:left;
}

table{
width:100%;
}

th{
background-color:orange;
color:white;
}

td{
color:green;
}