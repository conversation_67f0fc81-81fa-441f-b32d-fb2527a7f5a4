import { useState } from 'react'
import axios from 'axios'

function VoucherCreate() {
  const [formData, setFormData] = useState({
    voucherType: 'Sales',
    date: '',
    voucherNumber: '',
    partyLedger: '',
    amount: '',
    narration: '',
    items: [{ name: '', quantity: '', rate: '', amount: '' }]
  })
  const [loading, setLoading] = useState(false)
  const [message, setMessage] = useState('')
  const [messageType, setMessageType] = useState('')

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  const handleItemChange = (index, field, value) => {
    const newItems = [...formData.items]
    newItems[index][field] = value
    
    // Auto-calculate amount if quantity and rate are provided
    if (field === 'quantity' || field === 'rate') {
      const quantity = parseFloat(newItems[index].quantity) || 0
      const rate = parseFloat(newItems[index].rate) || 0
      newItems[index].amount = (quantity * rate).toFixed(2)
    }
    
    setFormData({
      ...formData,
      items: newItems
    })
  }

  const addItem = () => {
    setFormData({
      ...formData,
      items: [...formData.items, { name: '', quantity: '', rate: '', amount: '' }]
    })
  }

  const removeItem = (index) => {
    const newItems = formData.items.filter((_, i) => i !== index)
    setFormData({
      ...formData,
      items: newItems
    })
  }

  const calculateTotal = () => {
    return formData.items.reduce((total, item) => {
      return total + (parseFloat(item.amount) || 0)
    }, 0).toFixed(2)
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setLoading(true)
    setMessage('')
    
    try {
      const voucherData = {
        ...formData,
        totalAmount: calculateTotal()
      }
      
      const response = await axios.post('http://localhost:5000/api/create-voucher', voucherData)
      setMessage(response.data.message || 'Voucher created successfully!')
      setMessageType('success')
      
      // Reset form on success
      setFormData({
        voucherType: 'Sales',
        date: '',
        voucherNumber: '',
        partyLedger: '',
        amount: '',
        narration: '',
        items: [{ name: '', quantity: '', rate: '', amount: '' }]
      })
    } catch (err) {
      setMessage('Failed to create voucher. Please check if the backend server is running.')
      setMessageType('error')
      console.error('Error creating voucher:', err)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="container">
      <h1 className="title">Create Voucher</h1>
      
      <form onSubmit={handleSubmit}>
        <div className="form-group">
          <label htmlFor="voucherType">Voucher Type:</label>
          <select
            id="voucherType"
            name="voucherType"
            value={formData.voucherType}
            onChange={handleInputChange}
            className="form-control"
          >
            <option value="Sales">Sales</option>
            <option value="Purchase">Purchase</option>
          </select>
        </div>
        
        <div className="form-group">
          <label htmlFor="date">Date:</label>
          <input
            type="date"
            id="date"
            name="date"
            value={formData.date}
            onChange={handleInputChange}
            className="form-control"
            required
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="voucherNumber">Voucher Number:</label>
          <input
            type="text"
            id="voucherNumber"
            name="voucherNumber"
            value={formData.voucherNumber}
            onChange={handleInputChange}
            className="form-control"
            placeholder="Enter voucher number"
            required
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="partyLedger">Party Ledger:</label>
          <input
            type="text"
            id="partyLedger"
            name="partyLedger"
            value={formData.partyLedger}
            onChange={handleInputChange}
            className="form-control"
            placeholder="Enter party ledger name"
            required
          />
        </div>

        <div className="form-group">
          <label>Items:</label>
          {formData.items.map((item, index) => (
            <div key={index} style={{ border: '1px solid #ddd', padding: '1rem', marginBottom: '1rem', borderRadius: '4px' }}>
              <div style={{ display: 'grid', gridTemplateColumns: '2fr 1fr 1fr 1fr auto', gap: '1rem', alignItems: 'end' }}>
                <div>
                  <label>Item Name:</label>
                  <input
                    type="text"
                    value={item.name}
                    onChange={(e) => handleItemChange(index, 'name', e.target.value)}
                    className="form-control"
                    placeholder="Enter item name"
                    required
                  />
                </div>
                <div>
                  <label>Quantity:</label>
                  <input
                    type="number"
                    value={item.quantity}
                    onChange={(e) => handleItemChange(index, 'quantity', e.target.value)}
                    className="form-control"
                    placeholder="Qty"
                    step="0.01"
                    required
                  />
                </div>
                <div>
                  <label>Rate:</label>
                  <input
                    type="number"
                    value={item.rate}
                    onChange={(e) => handleItemChange(index, 'rate', e.target.value)}
                    className="form-control"
                    placeholder="Rate"
                    step="0.01"
                    required
                  />
                </div>
                <div>
                  <label>Amount:</label>
                  <input
                    type="number"
                    value={item.amount}
                    onChange={(e) => handleItemChange(index, 'amount', e.target.value)}
                    className="form-control"
                    placeholder="Amount"
                    step="0.01"
                    readOnly
                  />
                </div>
                <div>
                  {formData.items.length > 1 && (
                    <button
                      type="button"
                      onClick={() => removeItem(index)}
                      className="btn"
                      style={{ backgroundColor: '#dc3545', color: 'white' }}
                    >
                      Remove
                    </button>
                  )}
                </div>
              </div>
            </div>
          ))}
          
          <button type="button" onClick={addItem} className="btn btn-primary" style={{ marginBottom: '1rem' }}>
            Add Item
          </button>
        </div>

        <div className="form-group">
          <label><strong>Total Amount: ₹{calculateTotal()}</strong></label>
        </div>
        
        <div className="form-group">
          <label htmlFor="narration">Narration:</label>
          <textarea
            id="narration"
            name="narration"
            value={formData.narration}
            onChange={handleInputChange}
            className="form-control"
            placeholder="Enter narration (optional)"
            rows="3"
          />
        </div>
        
        <button type="submit" className="btn btn-success" disabled={loading}>
          {loading ? 'Creating...' : 'Create Voucher'}
        </button>
      </form>

      {message && (
        <div className={`alert ${messageType === 'success' ? 'alert-success' : 'alert-error'}`}>
          {message}
        </div>
      )}
    </div>
  )
}

export default VoucherCreate
