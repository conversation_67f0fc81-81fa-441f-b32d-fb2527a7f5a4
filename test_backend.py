import requests
import json
import time

def test_backend_connection():
    """Test if the backend server is running"""
    try:
        # Test a simple endpoint
        response = requests.post(
            'http://localhost:5000/api/vouchers',
            json={
                'voucherType': 'Sales',
                'fromDt': '2024-01-01',
                'toDt': '2024-01-31'
            },
            timeout=5
        )
        print(f"Backend Status Code: {response.status_code}")
        print(f"Backend Response: {response.text[:200]}...")
        return True
    except requests.exceptions.ConnectionError:
        print("Backend server is not running on localhost:5000")
        return False
    except Exception as e:
        print(f"Error testing backend: {e}")
        return False

def test_frontend_connection():
    """Test if the frontend server is running"""
    try:
        response = requests.get('http://localhost:5173', timeout=5)
        print(f"Frontend Status Code: {response.status_code}")
        return True
    except requests.exceptions.ConnectionError:
        print("Frontend server is not running on localhost:5173")
        return False
    except Exception as e:
        print(f"Error testing frontend: {e}")
        return False

def test_voucher_creation():
    """Test voucher creation functionality"""
    try:
        print("\n3. Testing Voucher Creation:")

        # Test Sales Voucher
        sales_data = {
            'voucherType': 'Sales',
            'date': '2024-01-15',
            'voucherNumber': 'TEST-S001',
            'partyLedger': 'Test Customer',
            'items': [
                {
                    'name': 'Test Product',
                    'quantity': '1',
                    'rate': '100.00',
                    'amount': '100.00'
                }
            ],
            'narration': 'Test sales voucher'
        }

        response = requests.post(
            'http://localhost:5000/api/create-voucher',
            json=sales_data,
            timeout=10
        )

        print(f"Sales Voucher Creation - Status Code: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"Sales Voucher Success: {result.get('success', False)}")
            print(f"Sales Voucher Message: {result.get('message', 'No message')}")
        else:
            print(f"Sales Voucher Error: {response.text}")

        # Test Purchase Voucher
        purchase_data = {
            'voucherType': 'Purchase',
            'date': '2024-01-15',
            'voucherNumber': 'TEST-P001',
            'partyLedger': 'Test Supplier',
            'items': [
                {
                    'name': 'Test Material',
                    'quantity': '2',
                    'rate': '50.00',
                    'amount': '100.00'
                }
            ],
            'narration': 'Test purchase voucher'
        }

        response = requests.post(
            'http://localhost:5000/api/create-voucher',
            json=purchase_data,
            timeout=10
        )

        print(f"Purchase Voucher Creation - Status Code: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"Purchase Voucher Success: {result.get('success', False)}")
            print(f"Purchase Voucher Message: {result.get('message', 'No message')}")
        else:
            print(f"Purchase Voucher Error: {response.text}")

        return True

    except Exception as e:
        print(f"Error testing voucher creation: {e}")
        return False

def test_other_endpoints():
    """Test other API endpoints"""
    try:
        print("\n4. Testing Other Endpoints:")

        # Test Create Ledger
        ledger_data = {
            'name': 'Test Ledger API',
            'group': 'Sundry Debtors',
            'address': 'Test Address',
            'country': 'India',
            'state': 'Test State',
            'mobile': '1234567890',
            'gstin': 'TEST123456789'
        }

        response = requests.post(
            'http://localhost:5000/api/create-ledger',
            json=ledger_data,
            timeout=10
        )
        print(f"Create Ledger - Status Code: {response.status_code}")

        # Test Create Stock Item
        stock_data = {
            'name': 'Test Stock Item API',
            'unit': 'Nos',
            'hsn': '1234',
            'gst': '18',
            'opening': '10'
        }

        response = requests.post(
            'http://localhost:5000/api/create-stock-item',
            json=stock_data,
            timeout=10
        )
        print(f"Create Stock Item - Status Code: {response.status_code}")

        # Test Ledger Statement
        ledger_statement_data = {
            'ledger': 'Cash',
            'fromDate': '2024-01-01',
            'toDate': '2024-01-31'
        }

        response = requests.post(
            'http://localhost:5000/api/ledger-statement',
            json=ledger_statement_data,
            timeout=10
        )
        print(f"Ledger Statement - Status Code: {response.status_code}")

        # Test Daybook
        daybook_data = {
            'fromDate': '2024-01-01',
            'toDate': '2024-01-31'
        }

        response = requests.post(
            'http://localhost:5000/api/daybook',
            json=daybook_data,
            timeout=10
        )
        print(f"Daybook - Status Code: {response.status_code}")

        return True

    except Exception as e:
        print(f"Error testing other endpoints: {e}")
        return False

if __name__ == "__main__":
    print("Comprehensive Testing of Tally Management System")
    print("=" * 50)

    print("\n1. Testing Backend Server:")
    backend_running = test_backend_connection()

    print("\n2. Testing Frontend Server:")
    frontend_running = test_frontend_connection()

    if backend_running:
        voucher_test = test_voucher_creation()
        other_tests = test_other_endpoints()
    else:
        voucher_test = False
        other_tests = False

    print(f"\n" + "=" * 50)
    print(f"SUMMARY:")
    print(f"Backend Running: {backend_running}")
    print(f"Frontend Running: {frontend_running}")
    print(f"Voucher Creation: {voucher_test}")
    print(f"Other Endpoints: {other_tests}")
    print(f"=" * 50)

    if all([backend_running, frontend_running, voucher_test]):
        print("\n✅ All tests passed! The application is ready to use.")
        print("\nFrontend URL: http://localhost:5173")
        print("Backend URL: http://localhost:5000")
        print("\nMake sure Tally is running on port 9000 for full functionality.")
    else:
        print("\n❌ Some tests failed. Please check the issues above.")
