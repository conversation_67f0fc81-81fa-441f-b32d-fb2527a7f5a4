import requests
import json

def test_backend_connection():
    """Test if the backend server is running"""
    try:
        # Test a simple endpoint
        response = requests.post(
            'http://localhost:5000/api/vouchers',
            json={
                'voucherType': 'Sales',
                'fromDt': '2024-01-01',
                'toDt': '2024-01-31'
            },
            timeout=5
        )
        print(f"Backend Status Code: {response.status_code}")
        print(f"Backend Response: {response.text}")
        return True
    except requests.exceptions.ConnectionError:
        print("Backend server is not running on localhost:5000")
        return False
    except Exception as e:
        print(f"Error testing backend: {e}")
        return False

def test_frontend_connection():
    """Test if the frontend server is running"""
    try:
        response = requests.get('http://localhost:5173', timeout=5)
        print(f"Frontend Status Code: {response.status_code}")
        return True
    except requests.exceptions.ConnectionError:
        print("Frontend server is not running on localhost:5173")
        return False
    except Exception as e:
        print(f"Error testing frontend: {e}")
        return False

if __name__ == "__main__":
    print("Testing Backend and Frontend Servers")
    print("=" * 40)
    
    print("\n1. Testing Backend Server:")
    backend_running = test_backend_connection()
    
    print("\n2. Testing Frontend Server:")
    frontend_running = test_frontend_connection()
    
    print(f"\nSummary:")
    print(f"Backend Running: {backend_running}")
    print(f"Frontend Running: {frontend_running}")
