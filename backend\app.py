from flask import Flask, request, jsonify
from flask_cors import CORS
import requests
from xml.etree import ElementTree as Et
import traceback
import sys
import os

# Add the parent directory to the path to import voucher module
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from voucher import TallyVoucherManager

app = Flask(__name__)
CORS(app)  # Enable CORS for all routes

# Tally server configuration
TALLY_URL = "http://localhost:9000"

class VoucherModel:
    def __init__(self, date, v_type, v_no, party_ledger, amount):
        self.date = date
        self.v_type = v_type
        self.v_no = v_no
        self.party_ledger = party_ledger
        self.amount = amount

def get_tally_data(payload):
    """Send request to Tally and return response"""
    try:
        req = requests.post(url=TALLY_URL, data=payload, timeout=30)
        res = req.text.encode("UTF-8")
        return res
    except requests.exceptions.RequestException as e:
        raise Exception(f"Failed to connect to Tally: {str(e)}")

@app.route('/api/vouchers', methods=['POST'])
def get_vouchers():
    """Get list of vouchers based on type and date range"""
    try:
        data = request.json
        vch_type = data.get('voucherType')
        from_dt = data.get('fromDt')
        to_dt = data.get('toDt')
        
        if not all([vch_type, from_dt, to_dt]):
            return jsonify({'error': 'Missing required fields'}), 400
        
        # Convert date format from YYYY-MM-DD to YYYYMMDD
        from_dt_formatted = from_dt.replace('-', '')
        to_dt_formatted = to_dt.replace('-', '')
        
        payload = get_voucher_payload(vch_type, from_dt_formatted, to_dt_formatted)
        response_data = get_tally_data(payload)
        
        res = Et.fromstring(response_data)
        voucher_models = []
        
        for vch in res.findall("./BODY/DATA/TALLYMESSAGE/VOUCHER"):
            try:
                if len(vch.findall("ALLLEDGERENTRIES.LIST")) == 0:
                    amount = vch.findall("LEDGERENTRIES.LIST")[0].find("AMOUNT").text
                else:
                    amount = vch.findall("ALLLEDGERENTRIES.LIST")[0].find("AMOUNT").text
                
                voucher_models.append({
                    'date': format_date(vch.find("DATE").text),
                    'v_type': vch.find("VOUCHERTYPENAME").text,
                    'v_no': vch.find("VOUCHERNUMBER").text,
                    'party_ledger': vch.find("PARTYLEDGERNAME").text,
                    'amount': format_amount(float(amount)) if amount else '0'
                })
            except Exception as e:
                print(f"Error processing voucher: {e}")
                continue
        
        return jsonify({
            'vouchers': voucher_models,
            'vch_type': vch_type,
            'from_dt': from_dt,
            'to_dt': to_dt
        })
        
    except Exception as e:
        print(f"Error in get_vouchers: {traceback.format_exc()}")
        return jsonify({'error': str(e)}), 500

def get_voucher_payload(v_type, from_dt, to_dt):
    """Generate XML payload for voucher request"""
    xml = "<ENVELOPE><HEADER><VERSION>1</VERSION><TALLYREQUEST>EXPORT</TALLYREQUEST><TYPE>DATA</TYPE>"
    xml += "<ID>VoucherRegister</ID></HEADER><BODY><DESC><STATICVARIABLES>"
    xml += f"<SVEXPORTFORMAT>$$SysName:XML</SVEXPORTFORMAT><SVFROMDATE Type='DATE'>{from_dt}</SVFROMDATE><SVTODATE Type='DATE'>{to_dt}</SVTODATE><VOUCHERTYPENAME>{v_type}</VOUCHERTYPENAME></STATICVARIABLES>"
    xml += "</DESC></BODY></ENVELOPE>"
    return xml

@app.route('/api/ledger-statement', methods=['POST'])
def get_ledger_statement():
    """Get ledger statement for a specific ledger and date range"""
    try:
        data = request.json
        ledger = data.get('ledger')
        from_date = data.get('fromDate')
        to_date = data.get('toDate')
        
        if not all([ledger, from_date, to_date]):
            return jsonify({'error': 'Missing required fields'}), 400
        
        # Convert date format
        from_date_formatted = from_date.replace('-', '')
        to_date_formatted = to_date.replace('-', '')
        
        payload = get_ledger_payload(from_date_formatted, to_date_formatted, ledger)
        response_data = get_tally_data(payload)
        
        xml = Et.fromstring(response_data)
        entries = []
        
        dates = [date.text for date in xml.findall("DSPVCHDATE")]
        ledgers = [led.text for led in xml.findall("DSPVCHLEDACCOUNT")]
        vtypes = [vtype.text for vtype in xml.findall("DSPVCHTYPE")]
        debits = [dr.text if dr.text else "0" for dr in xml.findall("DSPVCHDRAMT")]
        credits = [cr.text if cr.text else "0" for cr in xml.findall("DSPVCHCRAMT")]
        vch_nos = [vch.text for vch in xml.findall("DSPEXPLVCHNUMBER")]
        
        for i in range(len(dates)):
            entries.append({
                'date': format_date(dates[i]) if i < len(dates) else '',
                'voucher_no': vch_nos[i] if i < len(vch_nos) else '',
                'ledger': ledgers[i] if i < len(ledgers) else '',
                'voucher_type': vtypes[i] if i < len(vtypes) else '',
                'debit': debits[i] if i < len(debits) else '0',
                'credit': credits[i] if i < len(credits) else '0'
            })
        
        return jsonify({
            'ledger_name': ledger,
            'from_date': from_date,
            'to_date': to_date,
            'entries': entries
        })
        
    except Exception as e:
        print(f"Error in get_ledger_statement: {traceback.format_exc()}")
        return jsonify({'error': str(e)}), 500

def get_ledger_payload(fromdate, todate, ledger):
    """Generate XML payload for ledger statement request"""
    xml = "<ENVELOPE><HEADER><VERSION>1</VERSION><TALLYREQUEST>EXPORT</TALLYREQUEST><TYPE>DATA</TYPE>"
    xml += "<ID>LedgerVouchers</ID></HEADER><BODY><DESC><STATICVARIABLES>"
    xml += f"<SVEXPORTFORMAT>$$SysName:XML</SVEXPORTFORMAT><LEDGERNAME>{ledger}</LEDGERNAME><EXPLODEVNUM>YES</EXPLODEVNUM></STATICVARIABLES><TDL>"
    xml += f"<TDLMESSAGE><REPORT Name='LedgerVouchers' ISMODIFY='Yes'><SET>SVFROMDATE:'{fromdate}'</SET>"
    xml += f"<SET>SVTODATE:'{todate}'</SET></REPORT></TDLMESSAGE></TDL></DESC></BODY></ENVELOPE>"
    return xml

def format_date(date_str):
    """Format date from YYYYMMDD to DD-MM-YYYY"""
    if date_str and len(date_str) == 8:
        return f"{date_str[6:8]}-{date_str[4:6]}-{date_str[0:4]}"
    return date_str

def format_amount(amt):
    """Format amount with Dr/Cr suffix"""
    if amt < 0:
        return f"{abs(amt):.2f} Cr"
    return f"{amt:.2f} Dr"

@app.route('/api/create-ledger', methods=['POST'])
def create_ledger():
    """Create a new ledger in Tally"""
    try:
        data = request.json
        name = data.get('name')
        group = data.get('group', 'Sundry Debtors')
        address = data.get('address', '')
        country = data.get('country', '')
        state = data.get('state', '')
        mobile = data.get('mobile', '')
        gstin = data.get('gstin', '')

        if not name:
            return jsonify({'error': 'Ledger name is required'}), 400

        payload = get_create_ledger_payload(name, group, address, country, state, mobile, gstin)
        response_data = get_tally_data(payload)

        return jsonify({
            'message': 'Ledger created successfully',
            'response': response_data.decode('utf-8')
        })

    except Exception as e:
        print(f"Error in create_ledger: {traceback.format_exc()}")
        return jsonify({'error': str(e)}), 500

def get_create_ledger_payload(name, group, address, country, state, mobile, gstin):
    """Generate XML payload for creating ledger"""
    xml = '<ENVELOPE><HEADER><TALLYREQUEST>Import Data</TALLYREQUEST></HEADER><BODY>'
    xml += '<IMPORTDATA><REQUESTDESC><REPORTNAME>All Masters</REPORTNAME></REQUESTDESC><REQUESTDATA>'
    xml += f'<TALLYMESSAGE xmlns:UDF="TallyUDF"><LEDGER Action="Create"><NAME>{name}</NAME><PARENT>{group}</PARENT>'
    xml += f'<ADDRESS>{address}</ADDRESS><COUNTRYOFRESIDENCE>{country}</COUNTRYOFRESIDENCE>'
    xml += f'<LEDSTATENAME>{state}</LEDSTATENAME><LEDGERMOBILE>{mobile}</LEDGERMOBILE>'
    xml += f'<PARTYGSTIN>{gstin}</PARTYGSTIN></LEDGER></TALLYMESSAGE></REQUESTDATA></IMPORTDATA></BODY></ENVELOPE>'
    return xml

@app.route('/api/create-stock-item', methods=['POST'])
def create_stock_item():
    """Create a new stock item in Tally"""
    try:
        data = request.json
        name = data.get('name')
        unit = data.get('unit')
        hsn = data.get('hsn')
        gst = float(data.get('gst', 0))
        opening = data.get('opening', '0')

        if not all([name, unit, hsn]):
            return jsonify({'error': 'Name, unit, and HSN are required'}), 400

        payload = get_create_stock_item_payload(name, unit, hsn, gst, opening)
        response_data = get_tally_data(payload)

        return jsonify({
            'message': 'Stock item created successfully',
            'response': response_data.decode('utf-8')
        })

    except Exception as e:
        print(f"Error in create_stock_item: {traceback.format_exc()}")
        return jsonify({'error': str(e)}), 500

def get_create_stock_item_payload(name, unit, hsn, gst, opening):
    """Generate XML payload for creating stock item"""
    xml = '<ENVELOPE><HEADER><TALLYREQUEST>Import Data</TALLYREQUEST></HEADER><BODY><IMPORTDATA><REQUESTDESC><REPORTNAME>All Masters</REPORTNAME></REQUESTDESC><REQUESTDATA>'
    xml += f'<TALLYMESSAGE xmlns:UDF="TallyUDF"><STOCKITEM Action="Create"><NAME>{name}</NAME><BASEUNITS>{unit}</BASEUNITS><OPENINGBALANCE>{opening}</OPENINGBALANCE>'
    xml += '<GSTAPPLICABLE>&#4; Applicable</GSTAPPLICABLE><GSTDETAILS.LIST><APPLICABLEFROM>20200401</APPLICABLEFROM><CALCULATIONTYPE>On Value</CALCULATIONTYPE>'
    xml += f'<HSNCODE>{hsn}</HSNCODE><TAXABILITY>Taxable</TAXABILITY><STATEWISEDETAILS.LIST><STATENAME>&#4; Any</STATENAME>'
    xml += f'<RATEDETAILS.LIST><GSTRATEDUTYHEAD>Central Tax</GSTRATEDUTYHEAD><GSTRATEVALUATIONTYPE>Based on Value</GSTRATEVALUATIONTYPE><GSTRATE>{gst/2}</GSTRATE></RATEDETAILS.LIST>'
    xml += f'<RATEDETAILS.LIST><GSTRATEDUTYHEAD>State Tax</GSTRATEDUTYHEAD><GSTRATEVALUATIONTYPE>Based on Value</GSTRATEVALUATIONTYPE><GSTRATE>{gst/2}</GSTRATE></RATEDETAILS.LIST>'
    xml += f'<RATEDETAILS.LIST><GSTRATEDUTYHEAD>Integrated Tax</GSTRATEDUTYHEAD><GSTRATEVALUATIONTYPE>Based on Value</GSTRATEVALUATIONTYPE><GSTRATE>{gst}</GSTRATE></RATEDETAILS.LIST>'
    xml += '<RATEDETAILS.LIST><GSTRATEDUTYHEAD>Cess</GSTRATEDUTYHEAD><GSTRATEVALUATIONTYPE>Based on Value</GSTRATEVALUATIONTYPE></RATEDETAILS.LIST>'
    xml += '</STATEWISEDETAILS.LIST></GSTDETAILS.LIST></STOCKITEM></TALLYMESSAGE></REQUESTDATA></IMPORTDATA></BODY></ENVELOPE>'
    return xml

@app.route('/api/daybook', methods=['POST'])
def get_daybook():
    """Get daybook entries for a date range"""
    try:
        data = request.json
        from_date = data.get('fromDate')
        to_date = data.get('toDate')

        if not all([from_date, to_date]):
            return jsonify({'error': 'From date and to date are required'}), 400

        # Convert date format
        from_date_formatted = from_date.replace('-', '')
        to_date_formatted = to_date.replace('-', '')

        payload = get_daybook_payload(from_date_formatted, to_date_formatted)
        response_data = get_tally_data(payload)

        daybook_res = Et.fromstring(response_data)
        entries = []

        for vch in daybook_res.findall("./BODY/DATA/TALLYMESSAGE/VOUCHER"):
            try:
                if len(vch.findall("ALLLEDGERENTRIES.LIST")) == 0:
                    amount = vch.findall("LEDGERENTRIES.LIST")[0].find("AMOUNT").text
                else:
                    amount = vch.findall("ALLLEDGERENTRIES.LIST")[0].find("AMOUNT").text

                entries.append({
                    'date': vch.find("DATE").text,
                    'voucher_type': vch.find("VOUCHERTYPENAME").text,
                    'voucher_number': vch.find("VOUCHERNUMBER").text,
                    'party_ledger': vch.find("PARTYLEDGERNAME").text,
                    'amount': amount if amount else '0'
                })
            except Exception as e:
                print(f"Error processing daybook entry: {e}")
                continue

        return jsonify({
            'from_date': from_date,
            'to_date': to_date,
            'entries': entries
        })

    except Exception as e:
        print(f"Error in get_daybook: {traceback.format_exc()}")
        return jsonify({'error': str(e)}), 500

def get_daybook_payload(from_date, to_date):
    """Generate XML payload for daybook request"""
    xml = "<ENVELOPE><HEADER><VERSION>1</VERSION><TALLYREQUEST>EXPORT</TALLYREQUEST><TYPE>DATA</TYPE><ID>DayBook</ID>"
    xml += "</HEADER><BODY><DESC><STATICVARIABLES><SVEXPORTFORMAT>$$SysName:XML</SVEXPORTFORMAT>"
    xml += f"<SVFROMDATE TYPE='DATE'>{from_date}</SVFROMDATE><SVTODATE TYPE='DATE'>{to_date}</SVTODATE>"
    xml += "</STATICVARIABLES></DESC></BODY></ENVELOPE>"
    return xml

@app.route('/api/create-voucher', methods=['POST'])
def create_voucher():
    """Create a new voucher (Sales or Purchase) in Tally"""
    try:
        data = request.json
        voucher_type = data.get('voucherType')

        if not voucher_type:
            return jsonify({'error': 'Voucher type is required'}), 400

        # Initialize voucher manager
        voucher_manager = TallyVoucherManager(TALLY_URL)

        # Prepare voucher data
        voucher_data = {
            'date': data.get('date'),
            'voucher_number': data.get('voucherNumber'),
            'party_ledger': data.get('partyLedger'),
            'items': data.get('items', []),
            'narration': data.get('narration', '')
        }

        # Validate required fields
        if not all([voucher_data['date'], voucher_data['voucher_number'], voucher_data['party_ledger']]):
            return jsonify({'error': 'Date, voucher number, and party ledger are required'}), 400

        if not voucher_data['items']:
            return jsonify({'error': 'At least one item is required'}), 400

        # Create voucher
        result = voucher_manager.create_voucher(voucher_type, voucher_data)

        if result['success']:
            return jsonify({
                'message': result['message'],
                'success': True,
                'response': result['response']
            })
        else:
            return jsonify({
                'error': result['message'],
                'success': False
            }), 500

    except Exception as e:
        print(f"Error in create_voucher: {traceback.format_exc()}")
        return jsonify({'error': str(e)}), 500

if __name__ == "__main__":
    app.run(debug=True, port=5000)
