import { useState } from 'react'
import axios from 'axios'

function CreateLedger() {
  const [formData, setFormData] = useState({
    name: '',
    group: 'Sundry Debtors',
    address: '',
    country: '',
    state: '',
    mobile: '',
    gstin: ''
  })
  const [loading, setLoading] = useState(false)
  const [message, setMessage] = useState('')
  const [messageType, setMessageType] = useState('')

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setLoading(true)
    setMessage('')
    
    try {
      const response = await axios.post('http://localhost:5000/api/create-ledger', formData)
      setMessage(response.data.message || 'Ledger created successfully!')
      setMessageType('success')
      
      // Reset form on success
      setFormData({
        name: '',
        group: 'Sundry Debtors',
        address: '',
        country: '',
        state: '',
        mobile: '',
        gstin: ''
      })
    } catch (err) {
      setMessage('Failed to create ledger. Please check if the backend server is running.')
      setMessageType('error')
      console.error('Error creating ledger:', err)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="container">
      <h1 className="title">Create Ledger</h1>
      
      <form onSubmit={handleSubmit}>
        <div className="form-group">
          <label htmlFor="name">Ledger Name:</label>
          <input
            type="text"
            id="name"
            name="name"
            value={formData.name}
            onChange={handleInputChange}
            className="form-control"
            placeholder="Enter ledger name"
            required
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="group">Group Name:</label>
          <select
            id="group"
            name="group"
            value={formData.group}
            onChange={handleInputChange}
            className="form-control"
          >
            <option value="Sundry Debtors">Sundry Debtors</option>
            <option value="Sundry Creditors">Sundry Creditors</option>
          </select>
        </div>
        
        <div className="form-group">
          <label htmlFor="address">Ledger Address:</label>
          <input
            type="text"
            id="address"
            name="address"
            value={formData.address}
            onChange={handleInputChange}
            className="form-control"
            placeholder="Enter address"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="country">Ledger Country:</label>
          <input
            type="text"
            id="country"
            name="country"
            value={formData.country}
            onChange={handleInputChange}
            className="form-control"
            placeholder="Enter country"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="state">Ledger State:</label>
          <input
            type="text"
            id="state"
            name="state"
            value={formData.state}
            onChange={handleInputChange}
            className="form-control"
            placeholder="Enter state"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="mobile">Ledger Mobile:</label>
          <input
            type="tel"
            id="mobile"
            name="mobile"
            value={formData.mobile}
            onChange={handleInputChange}
            className="form-control"
            placeholder="Enter mobile number"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="gstin">Ledger GSTIN:</label>
          <input
            type="text"
            id="gstin"
            name="gstin"
            value={formData.gstin}
            onChange={handleInputChange}
            className="form-control"
            placeholder="Enter GSTIN"
          />
        </div>
        
        <button type="submit" className="btn btn-success" disabled={loading}>
          {loading ? 'Creating...' : 'Create Ledger'}
        </button>
      </form>

      {message && (
        <div className={`alert ${messageType === 'success' ? 'alert-success' : 'alert-error'}`}>
          {message}
        </div>
      )}
    </div>
  )
}

export default CreateLedger
