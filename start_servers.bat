@echo off
echo Starting Tally Management System
echo ================================

echo.
echo Starting Backend Server...
start "Backend Server" cmd /k "cd backend && python app.py"

echo.
echo Waiting for backend to start...
timeout /t 3 /nobreak > nul

echo.
echo Starting Frontend Server...
start "Frontend Server" cmd /k "cd frontend && npm run dev"

echo.
echo Both servers are starting...
echo.
echo Backend URL: http://localhost:5000
echo Frontend URL: http://localhost:5173
echo.
echo Make sure Tally is running on port 9000 for full functionality!
echo.
echo Press any key to exit this window...
pause > nul
