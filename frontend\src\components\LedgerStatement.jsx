import { useState } from 'react'
import axios from 'axios'

function LedgerStatement() {
  const [formData, setFormData] = useState({
    ledger: '',
    fromDate: '',
    toDate: ''
  })
  const [statement, setStatement] = useState(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setLoading(true)
    setError('')
    
    try {
      const response = await axios.post('http://localhost:5000/api/ledger-statement', formData)
      setStatement(response.data)
    } catch (err) {
      setError('Failed to fetch ledger statement. Please check if the backend server is running.')
      console.error('Error fetching ledger statement:', err)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="container">
      <h1 className="title">Ledger Statement</h1>
      
      <form onSubmit={handleSubmit}>
        <div className="form-group">
          <label htmlFor="ledger">Ledger Name:</label>
          <input
            type="text"
            id="ledger"
            name="ledger"
            value={formData.ledger}
            onChange={handleInputChange}
            className="form-control"
            placeholder="Enter ledger name"
            required
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="fromDate">From Date:</label>
          <input
            type="date"
            id="fromDate"
            name="fromDate"
            value={formData.fromDate}
            onChange={handleInputChange}
            className="form-control"
            required
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="toDate">To Date:</label>
          <input
            type="date"
            id="toDate"
            name="toDate"
            value={formData.toDate}
            onChange={handleInputChange}
            className="form-control"
            required
          />
        </div>
        
        <button type="submit" className="btn btn-primary" disabled={loading}>
          {loading ? 'Loading...' : 'Get Statement'}
        </button>
      </form>

      {error && (
        <div className="alert alert-error">
          {error}
        </div>
      )}

      {statement && (
        <div style={{ marginTop: '2rem' }}>
          <h3>Ledger Statement for {statement.ledger_name}</h3>
          <p><strong>Period:</strong> {statement.from_date} to {statement.to_date}</p>
          
          {statement.entries && statement.entries.length > 0 ? (
            <table className="table">
              <thead>
                <tr>
                  <th>Date</th>
                  <th>Voucher No</th>
                  <th>Ledger Account</th>
                  <th>Voucher Type</th>
                  <th>Debit</th>
                  <th>Credit</th>
                </tr>
              </thead>
              <tbody>
                {statement.entries.map((entry, index) => (
                  <tr key={index}>
                    <td>{entry.date}</td>
                    <td>{entry.voucher_no}</td>
                    <td>{entry.ledger}</td>
                    <td>{entry.voucher_type}</td>
                    <td>{entry.debit !== '0' ? entry.debit : ''}</td>
                    <td>{entry.credit !== '0' ? entry.credit : ''}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          ) : (
            <p>No entries found for the specified criteria.</p>
          )}
        </div>
      )}
    </div>
  )
}

export default LedgerStatement
