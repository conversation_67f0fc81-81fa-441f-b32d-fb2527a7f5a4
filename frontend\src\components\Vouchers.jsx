import { useState } from 'react'
import axios from 'axios'

function Vouchers() {
  const [formData, setFormData] = useState({
    voucherType: 'Sales',
    fromDt: '',
    toDt: ''
  })
  const [vouchers, setVouchers] = useState([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setLoading(true)
    setError('')
    
    try {
      const response = await axios.post('http://localhost:5000/api/vouchers', formData)
      setVouchers(response.data.vouchers || [])
    } catch (err) {
      setError('Failed to fetch vouchers. Please check if the backend server is running.')
      console.error('Error fetching vouchers:', err)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="container">
      <h1 className="title">Vouchers</h1>
      
      <form onSubmit={handleSubmit}>
        <div className="form-group">
          <label htmlFor="voucherType">Select voucher type:</label>
          <select
            id="voucherType"
            name="voucherType"
            value={formData.voucherType}
            onChange={handleInputChange}
            className="form-control"
          >
            <option value="Sales">Sales</option>
            <option value="Purchase">Purchase</option>
            <option value="Receipt">Receipt</option>
            <option value="Payment">Payment</option>
          </select>
        </div>
        
        <div className="form-group">
          <label htmlFor="fromDt">From date:</label>
          <input
            type="date"
            id="fromDt"
            name="fromDt"
            value={formData.fromDt}
            onChange={handleInputChange}
            className="form-control"
            required
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="toDt">To date:</label>
          <input
            type="date"
            id="toDt"
            name="toDt"
            value={formData.toDt}
            onChange={handleInputChange}
            className="form-control"
            required
          />
        </div>
        
        <button type="submit" className="btn btn-primary" disabled={loading}>
          {loading ? 'Loading...' : 'Submit'}
        </button>
      </form>

      {error && (
        <div className="alert alert-error">
          {error}
        </div>
      )}

      {vouchers.length > 0 && (
        <div style={{ marginTop: '2rem' }}>
          <h3>Voucher Results</h3>
          <table className="table">
            <thead>
              <tr>
                <th>Date</th>
                <th>Voucher Type</th>
                <th>Voucher Number</th>
                <th>Party Ledger</th>
                <th>Amount</th>
              </tr>
            </thead>
            <tbody>
              {vouchers.map((voucher, index) => (
                <tr key={index}>
                  <td>{voucher.date}</td>
                  <td>{voucher.v_type}</td>
                  <td>{voucher.v_no}</td>
                  <td>{voucher.party_ledger}</td>
                  <td>{voucher.amount}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  )
}

export default Vouchers
