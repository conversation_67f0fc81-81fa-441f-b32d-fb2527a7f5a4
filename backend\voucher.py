import requests
from xml.etree import ElementTree as Et
from datetime import datetime
import json

class TallyVoucherManager:
    """
    A comprehensive class for creating and managing vouchers in Tally
    Supports Sales and Purchase vouchers with multiple items
    """
    
    def __init__(self, tally_url="http://localhost:9000"):
        self.tally_url = tally_url
    
    def send_to_tally(self, xml_data):
        """Send XML data to Tally and return response"""
        try:
            response = requests.post(self.tally_url, data=xml_data, timeout=30)
            return response.text
        except requests.exceptions.RequestException as e:
            raise Exception(f"Failed to connect to Tally: {str(e)}")
    
    def format_date_for_tally(self, date_str):
        """Convert date from YYYY-MM-DD to YYYYMMDD format for Tally"""
        if isinstance(date_str, str):
            return date_str.replace('-', '')
        return date_str
    
    def create_sales_voucher(self, voucher_data):
        """
        Create a sales voucher in Tally
        
        voucher_data should contain:
        - date: Date in YYYY-MM-DD format
        - voucher_number: Voucher number
        - party_ledger: Customer ledger name
        - items: List of items with name, quantity, rate, amount
        - narration: Optional narration
        """
        try:
            date_formatted = self.format_date_for_tally(voucher_data['date'])
            
            xml = self._build_sales_voucher_xml(
                date_formatted,
                voucher_data['voucher_number'],
                voucher_data['party_ledger'],
                voucher_data['items'],
                voucher_data.get('narration', '')
            )
            
            response = self.send_to_tally(xml)
            return {
                'success': True,
                'message': 'Sales voucher created successfully',
                'response': response
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'Error creating sales voucher: {str(e)}',
                'response': None
            }
    
    def create_purchase_voucher(self, voucher_data):
        """
        Create a purchase voucher in Tally
        
        voucher_data should contain:
        - date: Date in YYYY-MM-DD format
        - voucher_number: Voucher number
        - party_ledger: Supplier ledger name
        - items: List of items with name, quantity, rate, amount
        - narration: Optional narration
        """
        try:
            date_formatted = self.format_date_for_tally(voucher_data['date'])
            
            xml = self._build_purchase_voucher_xml(
                date_formatted,
                voucher_data['voucher_number'],
                voucher_data['party_ledger'],
                voucher_data['items'],
                voucher_data.get('narration', '')
            )
            
            response = self.send_to_tally(xml)
            return {
                'success': True,
                'message': 'Purchase voucher created successfully',
                'response': response
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'Error creating purchase voucher: {str(e)}',
                'response': None
            }
    
    def _build_sales_voucher_xml(self, date, voucher_number, party_ledger, items, narration):
        """Build XML for sales voucher"""
        total_amount = sum(float(item.get('amount', 0)) for item in items)
        
        xml = '<ENVELOPE><HEADER><TALLYREQUEST>Import Data</TALLYREQUEST></HEADER><BODY>'
        xml += '<IMPORTDATA><REQUESTDESC><REPORTNAME>Vouchers</REPORTNAME></REQUESTDESC><REQUESTDATA>'
        xml += '<TALLYMESSAGE xmlns:UDF="TallyUDF">'
        xml += f'<VOUCHER REMOTEID="" VCHKEY="" VCHTYPE="Sales" ACTION="Create" OBJVIEW="Invoice Voucher View">'
        xml += f'<DATE>{date}</DATE>'
        xml += f'<VOUCHERTYPENAME>Sales</VOUCHERTYPENAME>'
        xml += f'<VOUCHERNUMBER>{voucher_number}</VOUCHERNUMBER>'
        xml += f'<PARTYLEDGERNAME>{party_ledger}</PARTYLEDGERNAME>'
        xml += f'<NARRATION>{narration}</NARRATION>'
        
        # Add inventory entries (items)
        for item in items:
            xml += '<ALLINVENTORYENTRIES.LIST>'
            xml += f'<STOCKITEMNAME>{item["name"]}</STOCKITEMNAME>'
            xml += f'<ISDEEMEDPOSITIVE>Yes</ISDEEMEDPOSITIVE>'
            xml += f'<RATE>{item["rate"]}</RATE>'
            xml += f'<AMOUNT>{item["amount"]}</AMOUNT>'
            xml += f'<ACTUALQTY>{item["quantity"]}</ACTUALQTY>'
            xml += f'<BILLEDQTY>{item["quantity"]}</BILLEDQTY>'
            xml += '</ALLINVENTORYENTRIES.LIST>'
        
        # Add ledger entries
        xml += '<ALLLEDGERENTRIES.LIST>'
        xml += f'<LEDGERNAME>{party_ledger}</LEDGERNAME>'
        xml += f'<ISDEEMEDPOSITIVE>Yes</ISDEEMEDPOSITIVE>'
        xml += f'<AMOUNT>{total_amount}</AMOUNT>'
        xml += '</ALLLEDGERENTRIES.LIST>'
        
        xml += '<ALLLEDGERENTRIES.LIST>'
        xml += '<LEDGERNAME>Sales</LEDGERNAME>'
        xml += '<ISDEEMEDPOSITIVE>No</ISDEEMEDPOSITIVE>'
        xml += f'<AMOUNT>-{total_amount}</AMOUNT>'
        xml += '</ALLLEDGERENTRIES.LIST>'
        
        xml += '</VOUCHER></TALLYMESSAGE></REQUESTDATA></IMPORTDATA></BODY></ENVELOPE>'
        return xml
    
    def _build_purchase_voucher_xml(self, date, voucher_number, party_ledger, items, narration):
        """Build XML for purchase voucher"""
        total_amount = sum(float(item.get('amount', 0)) for item in items)
        
        xml = '<ENVELOPE><HEADER><TALLYREQUEST>Import Data</TALLYREQUEST></HEADER><BODY>'
        xml += '<IMPORTDATA><REQUESTDESC><REPORTNAME>Vouchers</REPORTNAME></REQUESTDESC><REQUESTDATA>'
        xml += '<TALLYMESSAGE xmlns:UDF="TallyUDF">'
        xml += f'<VOUCHER REMOTEID="" VCHKEY="" VCHTYPE="Purchase" ACTION="Create" OBJVIEW="Invoice Voucher View">'
        xml += f'<DATE>{date}</DATE>'
        xml += f'<VOUCHERTYPENAME>Purchase</VOUCHERTYPENAME>'
        xml += f'<VOUCHERNUMBER>{voucher_number}</VOUCHERNUMBER>'
        xml += f'<PARTYLEDGERNAME>{party_ledger}</PARTYLEDGERNAME>'
        xml += f'<NARRATION>{narration}</NARRATION>'
        
        # Add inventory entries (items)
        for item in items:
            xml += '<ALLINVENTORYENTRIES.LIST>'
            xml += f'<STOCKITEMNAME>{item["name"]}</STOCKITEMNAME>'
            xml += f'<ISDEEMEDPOSITIVE>No</ISDEEMEDPOSITIVE>'
            xml += f'<RATE>{item["rate"]}</RATE>'
            xml += f'<AMOUNT>-{item["amount"]}</AMOUNT>'
            xml += f'<ACTUALQTY>-{item["quantity"]}</ACTUALQTY>'
            xml += f'<BILLEDQTY>-{item["quantity"]}</BILLEDQTY>'
            xml += '</ALLINVENTORYENTRIES.LIST>'
        
        # Add ledger entries
        xml += '<ALLLEDGERENTRIES.LIST>'
        xml += f'<LEDGERNAME>{party_ledger}</LEDGERNAME>'
        xml += f'<ISDEEMEDPOSITIVE>No</ISDEEMEDPOSITIVE>'
        xml += f'<AMOUNT>-{total_amount}</AMOUNT>'
        xml += '</ALLLEDGERENTRIES.LIST>'
        
        xml += '<ALLLEDGERENTRIES.LIST>'
        xml += '<LEDGERNAME>Purchase</LEDGERNAME>'
        xml += '<ISDEEMEDPOSITIVE>Yes</ISDEEMEDPOSITIVE>'
        xml += f'<AMOUNT>{total_amount}</AMOUNT>'
        xml += '</ALLLEDGERENTRIES.LIST>'
        
        xml += '</VOUCHER></TALLYMESSAGE></REQUESTDATA></IMPORTDATA></BODY></ENVELOPE>'
        return xml
    
    def create_voucher(self, voucher_type, voucher_data):
        """
        Generic method to create voucher based on type
        
        Args:
            voucher_type: 'Sales' or 'Purchase'
            voucher_data: Dictionary containing voucher details
        """
        if voucher_type.lower() == 'sales':
            return self.create_sales_voucher(voucher_data)
        elif voucher_type.lower() == 'purchase':
            return self.create_purchase_voucher(voucher_data)
        else:
            return {
                'success': False,
                'message': f'Unsupported voucher type: {voucher_type}',
                'response': None
            }

# Example usage and testing functions
def test_sales_voucher():
    """Test function for creating a sales voucher"""
    voucher_manager = TallyVoucherManager()
    
    sample_sales_data = {
        'date': '2024-01-15',
        'voucher_number': 'S001',
        'party_ledger': 'Customer ABC',
        'items': [
            {
                'name': 'Product A',
                'quantity': '2',
                'rate': '100.00',
                'amount': '200.00'
            },
            {
                'name': 'Product B',
                'quantity': '1',
                'rate': '150.00',
                'amount': '150.00'
            }
        ],
        'narration': 'Sales to Customer ABC'
    }
    
    result = voucher_manager.create_sales_voucher(sample_sales_data)
    print("Sales Voucher Result:", json.dumps(result, indent=2))
    return result

def test_purchase_voucher():
    """Test function for creating a purchase voucher"""
    voucher_manager = TallyVoucherManager()
    
    sample_purchase_data = {
        'date': '2024-01-15',
        'voucher_number': 'P001',
        'party_ledger': 'Supplier XYZ',
        'items': [
            {
                'name': 'Raw Material A',
                'quantity': '5',
                'rate': '50.00',
                'amount': '250.00'
            }
        ],
        'narration': 'Purchase from Supplier XYZ'
    }
    
    result = voucher_manager.create_purchase_voucher(sample_purchase_data)
    print("Purchase Voucher Result:", json.dumps(result, indent=2))
    return result

if __name__ == "__main__":
    print("Tally Voucher Manager - Testing")
    print("=" * 40)
    
    # Test sales voucher
    print("\n1. Testing Sales Voucher Creation:")
    test_sales_voucher()
    
    # Test purchase voucher
    print("\n2. Testing Purchase Voucher Creation:")
    test_purchase_voucher()
