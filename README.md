# Tally Management System

A comprehensive web-based application for managing Tally data with a modern React frontend and Python Flask backend. This system allows you to create vouchers, manage ledgers, view statements, and perform various Tally operations through a user-friendly web interface.

## Features

### 🎯 Core Functionality
- **Voucher Management**: View and filter vouchers by type and date range
- **Voucher Creation**: Create Sales and Purchase vouchers with multiple items
- **Ledger Management**: Create new ledgers with complete details
- **Ledger Statements**: Generate detailed ledger statements for any date range
- **Stock Item Management**: Create and manage stock items with GST details
- **Daybook**: View daily transaction summaries

### 🚀 Technical Features
- Modern React frontend with responsive design
- RESTful API backend with Flask
- Real-time communication with Tally via XML
- CORS enabled for cross-origin requests
- Comprehensive error handling
- Professional UI/UX design

## Project Structure

```
tally/
├── frontend/                 # React frontend application
│   ├── src/
│   │   ├── components/      # React components
│   │   │   ├── Home.jsx
│   │   │   ├── Vouchers.jsx
│   │   │   ├── VoucherCreate.jsx
│   │   │   ├── LedgerStatement.jsx
│   │   │   ├── CreateLedger.jsx
│   │   │   ├── StockItem.jsx
│   │   │   └── Daybook.jsx
│   │   ├── App.jsx          # Main app component
│   │   ├── App.css          # Styling
│   │   └── main.jsx         # Entry point
│   ├── package.json
│   └── vite.config.js
├── backend/                 # Python Flask backend
│   ├── app.py              # Main Flask application
│   ├── voucher.py          # Voucher management class
│   └── requirements.txt    # Python dependencies
├── voucher.py              # Standalone voucher utility
├── test_backend.py         # Comprehensive testing script
└── README.md               # This file
```

## Prerequisites

### Software Requirements
1. **Node.js** (v16 or higher) - for React frontend
2. **Python** (v3.8 or higher) - for Flask backend
3. **Tally ERP 9** or **TallyPrime** - running with Gateway enabled on port 9000

### Tally Configuration
1. Open Tally
2. Go to Gateway of Tally → F11 (Features) → Set "Enable Tally Gateway" to "Yes"
3. Set Port to 9000 (default)
4. Restart Tally

## Installation & Setup

### 1. Clone/Download the Project
```bash
# If using git
git clone <repository-url>
cd tally

# Or download and extract the project files
```

### 2. Backend Setup
```bash
# Navigate to backend directory
cd backend

# Install Python dependencies
pip install -r requirements.txt

# Start the backend server
python app.py
```
The backend will start on `http://localhost:5000`

### 3. Frontend Setup
```bash
# Navigate to frontend directory (in a new terminal)
cd frontend

# Install Node.js dependencies
npm install

# Start the development server
npm run dev
```
The frontend will start on `http://localhost:5173`

## Usage

### 1. Access the Application
Open your web browser and navigate to: `http://localhost:5173`

### 2. Available Features

#### Home Dashboard
- Overview of all available features
- Quick navigation to different modules

#### Vouchers
- View existing vouchers by type (Sales, Purchase, Receipt, Payment)
- Filter by date range
- Display voucher details in a table format

#### Create Voucher
- Create new Sales or Purchase vouchers
- Add multiple items with quantity, rate, and amount
- Automatic total calculation
- Send directly to Tally

#### Ledger Statement
- Generate ledger statements for specific accounts
- Filter by date range
- View debit/credit entries

#### Create Ledger
- Create new ledger accounts
- Set group (Sundry Debtors/Creditors)
- Add contact details and GST information

#### Stock Items
- Create new stock items
- Set HSN codes and GST rates
- Define opening balances

#### Daybook
- View daily transaction summaries
- Filter by date range
- See all voucher types in chronological order

## API Endpoints

### Backend API (http://localhost:5000)

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/vouchers` | POST | Get vouchers by type and date range |
| `/api/create-voucher` | POST | Create new Sales/Purchase voucher |
| `/api/ledger-statement` | POST | Get ledger statement |
| `/api/create-ledger` | POST | Create new ledger |
| `/api/create-stock-item` | POST | Create new stock item |
| `/api/daybook` | POST | Get daybook entries |

## Testing

### Automated Testing
Run the comprehensive test suite:
```bash
python test_backend.py
```

This will test:
- Backend server connectivity
- Frontend server connectivity
- Voucher creation functionality
- All API endpoints

### Manual Testing
1. Ensure Tally is running on port 9000
2. Start both backend and frontend servers
3. Access the web interface
4. Test each feature through the UI

## Troubleshooting

### Common Issues

#### 1. "Failed to connect to Tally" Error
- **Cause**: Tally is not running or Gateway is not enabled
- **Solution**: 
  - Start Tally
  - Enable Gateway (F11 → Features → Enable Tally Gateway: Yes)
  - Ensure port is set to 9000

#### 2. Backend Server Not Starting
- **Cause**: Missing Python dependencies or port conflict
- **Solution**:
  - Install requirements: `pip install -r backend/requirements.txt`
  - Check if port 5000 is available
  - Try running on a different port

#### 3. Frontend Not Loading
- **Cause**: Missing Node.js dependencies or port conflict
- **Solution**:
  - Install dependencies: `npm install` in frontend directory
  - Check if port 5173 is available
  - Clear browser cache

#### 4. CORS Errors
- **Cause**: Cross-origin request blocked
- **Solution**: Flask-CORS is already configured, restart backend server

### Error Messages

| Error | Meaning | Solution |
|-------|---------|----------|
| "Backend server is not running" | Flask server not accessible | Start backend with `python backend/app.py` |
| "Failed to connect to Tally" | Tally Gateway not accessible | Enable Tally Gateway on port 9000 |
| "Missing required fields" | Form validation failed | Fill all required fields |
| "Connection refused" | Port not accessible | Check if services are running on correct ports |

## Development

### Adding New Features
1. **Backend**: Add new routes in `backend/app.py`
2. **Frontend**: Create new components in `frontend/src/components/`
3. **Styling**: Update `frontend/src/App.css`

### Code Structure
- **Backend**: RESTful API with Flask, XML communication with Tally
- **Frontend**: React with functional components and hooks
- **Styling**: CSS with responsive design
- **State Management**: React useState hooks

## Production Deployment

### Backend Deployment
```bash
# Use a production WSGI server
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:5000 backend.app:app
```

### Frontend Deployment
```bash
# Build for production
cd frontend
npm run build

# Serve the built files with a web server
# The build files will be in the 'dist' directory
```

## License

This project is provided as-is for educational and commercial use.

## Support

For issues and questions:
1. Check the troubleshooting section
2. Run the test script to identify issues
3. Ensure all prerequisites are met
4. Verify Tally Gateway configuration

---

**Note**: This application requires Tally ERP 9 or TallyPrime to be running with Gateway enabled for full functionality. The web interface will work without Tally, but data operations will fail.
