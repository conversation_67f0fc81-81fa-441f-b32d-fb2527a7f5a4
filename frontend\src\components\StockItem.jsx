import { useState } from 'react'
import axios from 'axios'

function StockItem() {
  const [formData, setFormData] = useState({
    name: '',
    unit: '',
    hsn: '',
    gst: '',
    opening: ''
  })
  const [loading, setLoading] = useState(false)
  const [message, setMessage] = useState('')
  const [messageType, setMessageType] = useState('')

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setLoading(true)
    setMessage('')
    
    try {
      const response = await axios.post('http://localhost:5000/api/create-stock-item', formData)
      setMessage(response.data.message || 'Stock item created successfully!')
      setMessageType('success')
      
      // Reset form on success
      setFormData({
        name: '',
        unit: '',
        hsn: '',
        gst: '',
        opening: ''
      })
    } catch (err) {
      setMessage('Failed to create stock item. Please check if the backend server is running.')
      setMessageType('error')
      console.error('Error creating stock item:', err)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="container">
      <h1 className="title">Create Stock Item</h1>
      
      <form onSubmit={handleSubmit}>
        <div className="form-group">
          <label htmlFor="name">Item Name:</label>
          <input
            type="text"
            id="name"
            name="name"
            value={formData.name}
            onChange={handleInputChange}
            className="form-control"
            placeholder="Enter item name"
            required
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="unit">Item Unit:</label>
          <input
            type="text"
            id="unit"
            name="unit"
            value={formData.unit}
            onChange={handleInputChange}
            className="form-control"
            placeholder="Enter unit (e.g., Nos, Kg, Ltr)"
            required
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="hsn">Item HSN:</label>
          <input
            type="text"
            id="hsn"
            name="hsn"
            value={formData.hsn}
            onChange={handleInputChange}
            className="form-control"
            placeholder="Enter HSN code"
            required
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="gst">Item GST %:</label>
          <input
            type="number"
            id="gst"
            name="gst"
            value={formData.gst}
            onChange={handleInputChange}
            className="form-control"
            placeholder="Enter GST percentage"
            min="0"
            max="100"
            step="0.01"
            required
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="opening">Item Opening:</label>
          <input
            type="number"
            id="opening"
            name="opening"
            value={formData.opening}
            onChange={handleInputChange}
            className="form-control"
            placeholder="Enter opening balance"
            step="0.01"
            required
          />
        </div>
        
        <button type="submit" className="btn btn-success" disabled={loading}>
          {loading ? 'Creating...' : 'Create Stock Item'}
        </button>
      </form>

      {message && (
        <div className={`alert ${messageType === 'success' ? 'alert-success' : 'alert-error'}`}>
          {message}
        </div>
      )}
    </div>
  )
}

export default StockItem
