import { Link } from 'react-router-dom'

function Home() {
  return (
    <div className="container">
      <h1 className="title">Tally Management System</h1>
      <p style={{ textAlign: 'center', fontSize: '1.2rem', color: '#6c757d', marginBottom: '2rem' }}>
        Manage your Tally data with ease through our web interface
      </p>
      
      <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '2rem' }}>
        <div className="feature-card">
          <h3>Voucher Management</h3>
          <p>View and manage all types of vouchers including Sales, Purchase, Receipt, and Payment vouchers.</p>
          <Link to="/vouchers" className="btn btn-primary">View Vouchers</Link>
        </div>
        
        <div className="feature-card">
          <h3>Create Vouchers</h3>
          <p>Create new purchase and sales vouchers and send them directly to Tally.</p>
          <Link to="/create-voucher" className="btn btn-primary">Create Voucher</Link>
        </div>
        
        <div className="feature-card">
          <h3>Ledger Statements</h3>
          <p>Generate detailed ledger statements for any date range and specific ledger accounts.</p>
          <Link to="/ledger-statement" className="btn btn-primary">Ledger Statement</Link>
        </div>
        
        <div className="feature-card">
          <h3>Create Ledger</h3>
          <p>Create new ledger accounts with complete details including GST information.</p>
          <Link to="/create-ledger" className="btn btn-primary">Create Ledger</Link>
        </div>
        
        <div className="feature-card">
          <h3>Stock Items</h3>
          <p>Manage stock items with HSN codes, GST rates, and opening balances.</p>
          <Link to="/stock-item" className="btn btn-primary">Stock Items</Link>
        </div>
        
        <div className="feature-card">
          <h3>Daybook</h3>
          <p>View daily transaction summaries and voucher details for any date range.</p>
          <Link to="/daybook" className="btn btn-primary">Daybook</Link>
        </div>
      </div>
    </div>
  )
}

export default Home
