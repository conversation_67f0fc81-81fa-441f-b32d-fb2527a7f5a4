import { useState } from 'react'
import axios from 'axios'

function Daybook() {
  const [formData, setFormData] = useState({
    fromDate: '',
    toDate: ''
  })
  const [daybook, setDaybook] = useState(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setLoading(true)
    setError('')
    
    try {
      const response = await axios.post('http://localhost:5000/api/daybook', formData)
      setDaybook(response.data)
    } catch (err) {
      setError('Failed to fetch daybook. Please check if the backend server is running.')
      console.error('Error fetching daybook:', err)
    } finally {
      setLoading(false)
    }
  }

  const formatAmount = (amount) => {
    const amt = parseFloat(amount)
    if (amt < 0) {
      return `${Math.abs(amt).toFixed(2)} Cr`
    }
    return `${amt.toFixed(2)} Dr`
  }

  const formatDate = (dateStr) => {
    if (dateStr && dateStr.length === 8) {
      return `${dateStr.slice(6, 8)}-${dateStr.slice(4, 6)}-${dateStr.slice(0, 4)}`
    }
    return dateStr
  }

  return (
    <div className="container">
      <h1 className="title">Daybook</h1>
      
      <form onSubmit={handleSubmit}>
        <div className="form-group">
          <label htmlFor="fromDate">From Date:</label>
          <input
            type="date"
            id="fromDate"
            name="fromDate"
            value={formData.fromDate}
            onChange={handleInputChange}
            className="form-control"
            required
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="toDate">To Date:</label>
          <input
            type="date"
            id="toDate"
            name="toDate"
            value={formData.toDate}
            onChange={handleInputChange}
            className="form-control"
            required
          />
        </div>
        
        <button type="submit" className="btn btn-primary" disabled={loading}>
          {loading ? 'Loading...' : 'Get Daybook'}
        </button>
      </form>

      {error && (
        <div className="alert alert-error">
          {error}
        </div>
      )}

      {daybook && (
        <div style={{ marginTop: '2rem' }}>
          <h3>Daybook Entries</h3>
          <p><strong>Period:</strong> {daybook.from_date} to {daybook.to_date}</p>
          
          {daybook.entries && daybook.entries.length > 0 ? (
            <table className="table">
              <thead>
                <tr>
                  <th>Date</th>
                  <th>Particulars</th>
                  <th>Vch Type</th>
                  <th>Vch No</th>
                  <th>Amount</th>
                </tr>
              </thead>
              <tbody>
                {daybook.entries.map((entry, index) => (
                  <tr key={index}>
                    <td>{formatDate(entry.date)}</td>
                    <td>{entry.voucher_type}</td>
                    <td>{entry.voucher_number}</td>
                    <td>{entry.party_ledger}</td>
                    <td>{formatAmount(entry.amount)}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          ) : (
            <p>No entries found for the specified date range.</p>
          )}
        </div>
      )}
    </div>
  )
}

export default Daybook
